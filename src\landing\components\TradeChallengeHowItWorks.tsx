import Image from "next/image";

export default function TradeChallengeHowItWorks() {
  return (
    <section className="relative font-montserrat flex flex-col w-full max-w-[880px] items-center gap-10 mx-auto py-10">
      {/* Background blur effects */}
      <div className="absolute w-[239px] h-[239px] top-0 right-0 rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20"></div>
      <div className="absolute w-[381px] h-[381px] top-20 left-0 bg-[#d19049] rounded-[190.5px] blur-[159.41px] opacity-20"></div>
      
      <div className="flex flex-col lg:flex-row items-center gap-10 w-full">
        {/* How it Works Section */}
        <div className="w-full px-5">
          <h3 className="font-montserrat text-2xl font-semibold text-neutral-100 md:text-4xl">
            How it Works
          </h3>
          <p className="mb-5 md:text-lg text-neutral-300">
            Powered by Tradehouse BOT. Executing every trade 24/7
          </p>
          
          {/* Steps */}
          <div className="flex items-center gap-3 px-5 py-2.5 mb-2 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]">
            <span className="font-londrina-outline text-white font-bold text-[40px]">01 </span>
            <span className="font-montserrat font-normal text-neutral-300 text-[16px] md:text-xl text-center whitespace-nowrap">
              Buy Bitcoin/Solana
            </span>
          </div>
          
          <div className="flex items-center gap-3 px-5 py-2.5 mb-2 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]">
            <span className="font-londrina-outline text-white font-bold text-[40px]">02 </span>
            <span className="font-montserrat font-normal text-neutral-300 text-[16px] md:text-xl text-center whitespace-nowrap">
              Sell when it's up 1.5%
            </span>
          </div>
          
          <div className="flex items-center gap-3 px-5 py-2.5 mb-2 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]">
            <span className="font-londrina-outline text-white font-bold text-[40px]">03 </span>
            <span className="font-montserrat font-normal text-neutral-300 text-[16px] md:text-xl text-center whitespace-nowrap">
              Wait for a 20% dip to re-enter
            </span>
          </div>
          
          <div className="flex items-center gap-3 px-5 py-2.5 mb-2 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]">
            <span className="font-londrina-outline text-white font-bold text-[40px]">04 </span>
            <span className="font-montserrat font-normal text-neutral-300 text-[16px] md:text-xl text-center whitespace-nowrap">
              Repeat 1,000 times
            </span>
          </div>
          
          <div className="flex items-center gap-3 px-5 py-2.5 mb-2 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]">
            <span className="font-londrina-outline text-white font-bold text-[40px]">05 </span>
            <span className="font-montserrat font-normal text-neutral-300 text-[16px] md:text-xl text-center whitespace-nowrap">
              Never withdraw, Never Stop
            </span>
          </div>
        </div>
        
        {/* Backed by ESV Capital Section */}
        <div className="px-5">
          <h1 className="text-3xl font-semibold text-center lg:text-start text-neutral-100">
            Backed by ESV Capital
          </h1>
          <h1 className="bg-gradient-to-r from-[#D19049] to-[#BF4129] bg-clip-text text-transparent font-semibold text-xl text-center lg:text-2xl my-5 lg:text-start">
            A Real Experiment, Backed by Real Fund
          </h1>
          
          <div className="space-y-3 text-neutral-300">
            <div className="flex space-x-3">
              <span>•</span>
              <p className="self-stretch">
                ESVC Capital is not just an investment. It's the engine.
              </p>
            </div>
            <div className="flex space-x-3">
              <span>•</span>
              <p className="self-stretch">
                All Tradehouse BOT subscription revenue flows into the ESVC on-chain treasury.
              </p>
            </div>
            <div className="flex space-x-3">
              <span>•</span>
              <p className="self-stretch">
                Every trade is a proof of utility, not hype.
              </p>
            </div>
          </div>
          
          <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-background shadow-sm hover:bg-accent hover:text-accent-foreground w-full h-14 px-4 py-2.5 mt-5 rounded-[999px] text-[#C6741B] border border-solid border-neutral-700 font-montserrat font-semibold text-lg">
            View Public Treasury Wallet ➔
          </button>
        </div>
      </div>
      
      <Image
        className="absolute w-[39px] md:w-[78px] md:h-[52px] bottom-7 right-[20px] md:bottom-[0px] md:right-[250px]"
        alt="Element"
        src="/c.animaapp.com/mc62dpc6QTKBF1/img/element-09.svg"
        width={78}
        height={52}
      />
      
      <div className="flex justify-center items-center w-full">
        <button className="whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 shadow h-14 font-montserrat font-semibold text-[#171717] text-lg bg-[#C6741B] hover:bg-[#B8681A] rounded-full px-10 py-2.5 flex items-center justify-center gap-3">
          Get the Bot - $100/Year
        </button>
      </div>
    </section>
  );
}
