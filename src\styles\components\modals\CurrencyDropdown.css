/* Beautiful Currency Dropdown */
.currency-dropdown-container {
  position: relative;
  width: 100%;
  z-index: 200;
}

/* Dropdown Trigger */
.currency-dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 18px 24px;
  background: linear-gradient(135deg, #1A1A1A 0%, #262626 100%);
  border: 2px solid #404040;
  border-radius: 16px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  margin-bottom: 24px;
}

.currency-dropdown-trigger::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(191, 65, 41, 0.1) 0%, rgba(191, 65, 41, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.currency-dropdown-trigger:hover {
  border-color: #BF4129;
  background: linear-gradient(135deg, #262626 0%, #333333 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(191, 65, 41, 0.2);
}

.currency-dropdown-trigger:hover::before {
  opacity: 1;
}

.currency-dropdown-trigger:focus {
  border-color: #BF4129;
  box-shadow: 0 0 0 4px rgba(191, 65, 41, 0.2);
}

.currency-dropdown-container.open .currency-dropdown-trigger {
  border-color: #BF4129;
  background: linear-gradient(135deg, #262626 0%, #333333 100%);
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.currency-dropdown-container.open .currency-dropdown-trigger::before {
  opacity: 1;
}

/* Trigger Content */
.trigger-content {
  display: flex;
  align-items: center;
  gap: 0;
  flex: 1;
  position: relative;
  padding-left: 40px;
}

.currency-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(191, 65, 41, 0.1);
  border-radius: 6px;
  padding: 4px;
  border: 1px solid rgba(191, 65, 41, 0.2);
  flex-shrink: 0;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.currency-icon img {
  width: 18px;
  height: 18px;
  filter: brightness(0) invert(1);
}

.currency-info {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.currency-name {
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 1.2;
}

.currency-symbol {
  font-size: 14px;
  font-weight: 500;
  color: #CCCCCC;
  line-height: 1.2;
  margin-left: 4px;
}

.currency-placeholder {
  display: flex;
  align-items: center;
  gap: 12px;
}

.placeholder-icon {
  font-size: 24px;
}

.placeholder-text {
  font-size: 16px;
  font-weight: 500;
  color: #999999;
}

/* Dropdown Arrow */
.dropdown-arrow {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.dropdown-arrow img {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
}

/* Dropdown Menu */
.currency-dropdown-menu {
  position: absolute;
  top: calc(100% - 2px);
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #1A1A1A 0%, #262626 100%);
  border: 2px solid #BF4129;
  border-top: none;
  border-radius: 0 0 16px 16px;
  overflow: hidden;
  z-index: 9999;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
  animation: currencyDropdownSlideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes currencyDropdownSlideDown {
  from {
    opacity: 0;
    transform: translateY(-12px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Dropdown Options */
.currency-dropdown-option {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  padding: 18px 24px;
  background: none;
  border: none;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  position: relative;
  overflow: hidden;
}

.currency-dropdown-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(191, 65, 41, 0.1) 0%, rgba(191, 65, 41, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.currency-dropdown-option:hover {
  background: rgba(255, 255, 255, 0.05);
}

.currency-dropdown-option:hover::before {
  opacity: 1;
}

.currency-dropdown-option.selected {
  background: rgba(191, 65, 41, 0.15);
  color: #BF4129;
}

.currency-dropdown-option.selected::before {
  opacity: 1;
}

.currency-dropdown-option:not(:last-child) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Option Content */
.option-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(191, 65, 41, 0.1);
  border-radius: 8px;
  padding: 6px;
  flex-shrink: 0;
  border: 1px solid rgba(191, 65, 41, 0.2);
}

.option-icon img {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1);
}

.option-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.option-main {
  display: flex;
  align-items: center;
  gap: 6px;
}

.option-name {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.2;
}

.option-symbol {
  font-size: 14px;
  font-weight: 500;
  color: #CCCCCC;
  line-height: 1.2;
}

.option-network {
  font-size: 12px;
  font-weight: 500;
  color: #888888;
  line-height: 1.2;
}

.currency-dropdown-option.selected .option-symbol,
.currency-dropdown-option.selected .option-network {
  color: rgba(191, 65, 41, 0.8);
}

/* Check Icon */
.option-check {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #BF4129;
  flex-shrink: 0;
  background: rgba(191, 65, 41, 0.1);
  border-radius: 50%;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .currency-dropdown-trigger {
    padding: 16px 20px;
    border-radius: 12px;
    margin-bottom: 20px;
  }

  .trigger-content {
    padding-left: 36px;
  }

  .trigger-content {
    gap: 12px;
  }

  .currency-icon {
    width: 24px;
    height: 24px;
    padding: 3px;
  }

  .currency-icon img {
    width: 18px;
    height: 18px;
  }

  .currency-info {
    gap: 6px;
  }

  .currency-name {
    font-size: 15px;
  }

  .currency-symbol {
    font-size: 13px;
    margin-left: 2px;
  }

  .placeholder-text {
    font-size: 15px;
  }

  .dropdown-arrow {
    width: 20px;
    height: 20px;
  }

  .dropdown-arrow img {
    width: 14px;
    height: 14px;
  }

  .currency-dropdown-menu {
    border-radius: 0 0 12px 12px;
  }

  .currency-dropdown-option {
    padding: 16px 20px;
    gap: 12px;
  }

  .option-icon {
    width: 28px;
    height: 28px;
    padding: 4px;
  }

  .option-icon img {
    width: 18px;
    height: 18px;
  }

  .option-name {
    font-size: 15px;
  }

  .option-symbol {
    font-size: 13px;
  }

  .option-network {
    font-size: 11px;
  }

  .option-check {
    width: 20px;
    height: 20px;
  }
}
