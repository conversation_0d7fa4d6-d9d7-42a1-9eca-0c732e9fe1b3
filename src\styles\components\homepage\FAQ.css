.faq {
  background: var(--bg-primary);
  padding: 80px 0;
}

.faq-header {
  text-align: center;
  margin-bottom: 60px;
}

.faq-title {
  font-size: 48px;
  font-weight: 700;
  color: var(--text-primary);
}

.faq-content {
  max-width: 800px;
  margin: 0 auto 60px;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.faq-item {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item:hover {
  border-color: var(--accent-orange);
}

.faq-item.open {
  border-color: var(--accent-orange);
  box-shadow: 0 4px 15px rgba(232, 90, 79, 0.1);
}

.faq-question {
  width: 100%;
  padding: 24px;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.faq-question:hover {
  background: rgba(232, 90, 79, 0.05);
}

.question-number {
  color: var(--text-secondary);
  font-weight: 500;
  min-width: 24px;
}

.question-text {
  flex: 1;
}

.question-icon {
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 300;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.faq-item.open .question-icon {
  background: var(--accent-orange);
  border-color: var(--accent-orange);
  color: white;
  transform: rotate(180deg);
}

.faq-answer {
  padding: 0 24px 24px;
  animation: slideDown 0.3s ease;
}

.faq-answer p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
  padding-left: 40px; /* Align with question text */
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Action Buttons */
.faq-actions {
  display: flex;
  justify-content: center;
  gap: 24px;
  flex-wrap: wrap;
}

.faq-actions .btn-primary,
.faq-actions .btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  padding: 16px 32px;
}

/* Responsive */
@media (max-width: 768px) {
  .faq {
    padding: 60px 0 !important;
  }

  .faq-header {
    margin-bottom: 40px !important;
    padding: 0 20px !important;
  }

  .faq-title {
    font-size: 32px !important;
    margin-bottom: 16px !important;
    line-height: 1.2 !important;
  }

  .faq-content {
    padding: 0 20px !important;
    margin-bottom: 40px !important;
  }

  .faq-question {
    font-size: 16px !important;
    padding: 20px !important;
    gap: 12px !important;
  }

  .faq-answer {
    padding: 0 20px 20px !important;
  }

  .faq-answer p {
    padding-left: 32px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
  }

  .faq-actions {
    flex-direction: column !important;
    align-items: center !important;
    padding: 0 20px !important;
    gap: 16px !important;
  }

  .faq-actions .btn-primary,
  .faq-actions .btn-secondary {
    width: 100% !important;
    max-width: 320px !important;
    justify-content: center !important;
    padding: 16px 24px !important;
    font-size: 16px !important;
    border-radius: 12px !important;
  }
}
