@import "tailwindcss";

/* Font imports */
@font-face {
  font-family: '<PERSON><PERSON><PERSON> Outline';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/fonts/595e4c7b8597e85f-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/fonts/4f05ba3a6752a328.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

:root {
  --background: #171717;
  --foreground: #ffffff;
  --font-montserrat: 'Montserrat', sans-serif;
  --font-londrina-outline: 'Londrina Outline', sans-serif;

  /* Color variables matching the legacy site */
  --color-neutral-50: oklch(98.5% 0 0);
  --color-neutral-100: oklch(97% 0 0);
  --color-neutral-300: oklch(87% 0 0);
  --color-neutral-500: oklch(55.6% 0 0);
  --color-neutral-600: oklch(43.9% 0 0);
  --color-neutral-700: oklch(37.1% 0 0);
  --color-neutral-800: oklch(26.9% 0 0);
  --color-neutral-900: oklch(20.5% 0 0);
  --color-primary: #bf4129;
  --color-primary-hover: #a83a25;
  --color-secondary: #d19049;
  --color-accent: #cc6754;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-montserrat);
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-montserrat);
  margin: 0;
  padding: 0;
}

/* Custom utility classes for exact replication */
.font-montserrat {
  font-family: var(--font-montserrat);
}

.font-londrina-outline {
  font-family: var(--font-londrina-outline);
}

/* Background colors */
.bg-primary {
  background-color: var(--color-primary);
}

.bg-primary-hover:hover {
  background-color: var(--color-primary-hover);
}

.bg-secondary {
  background-color: var(--color-secondary);
}

.bg-accent {
  background-color: var(--color-accent);
}

/* Custom gradient backgrounds */
.bg-gradient-primary {
  background: linear-gradient(38deg, rgba(209,144,73,1) 36%, rgba(191,65,41,1) 100%);
}

.bg-gradient-blur {
  background: linear-gradient(55deg, rgba(209,144,73,1) 30%, rgba(191,65,41,1) 100%);
}

/* Custom border gradients */
.border-gradient-primary {
  border-image: linear-gradient(38deg, rgba(209,144,73,1) 36%, rgba(191,65,41,1) 100%) 1;
}

.border-gradient-neutral {
  border-image: linear-gradient(249deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 54%, rgba(255,255,255,1) 100%) 1;
}

/* Specific color overrides for exact matching */
.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-secondary);
}

.text-accent {
  color: var(--color-accent);
}

/* Custom animations and transitions */
.transition-transform-300 {
  transition: transform 300ms ease-in-out;
}

/* Blur effects */
.blur-100 {
  filter: blur(100px);
}

.blur-97 {
  filter: blur(97.91px);
}

.blur-159 {
  filter: blur(159.41px);
}

.blur-33 {
  filter: blur(33.05px);
}

/* Custom positioning and sizing */
.rounded-999 {
  border-radius: 999px;
}

.rounded-981 {
  border-radius: 981.72px;
}

/* Specific background colors from the legacy site */
.bg-dark-brown {
  background-color: #1d1104;
}

.bg-dark-section {
  background-color: #281705;
}

.bg-mobile-menu {
  background-color: #262626;
}

.bg-mobile-item {
  background-color: #3a3a3a;
}
