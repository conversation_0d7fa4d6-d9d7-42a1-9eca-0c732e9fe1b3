import React from 'react';
import styles from '../../styles/components.module.css';

interface RegularCSSExampleProps {
  title: string;
  description: string;
  onButtonClick?: () => void;
}

/**
 * Example component using CSS modules for styling
 * This demonstrates how regular CSS can coexist with Tailwind
 * without conflicts using CSS modules
 */
export const RegularCSSExample: React.FC<RegularCSSExampleProps> = ({
  title,
  description,
  onButtonClick
}) => {
  return (
    <div className={styles.card}>
      <h2 className={styles.heading}>{title}</h2>
      <p className={`${styles.text} ${styles.textMuted}`}>{description}</p>
      <div style={{ marginTop: '16px' }}>
        <button 
          className={styles.btnPrimary}
          onClick={onButtonClick}
        >
          Primary Action
        </button>
        <button 
          className={styles.btnSecondary}
          style={{ marginLeft: '12px' }}
        >
          Secondary Action
        </button>
      </div>
    </div>
  );
};

export default RegularCSSExample;
