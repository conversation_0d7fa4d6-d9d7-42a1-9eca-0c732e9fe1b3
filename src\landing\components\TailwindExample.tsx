import React from 'react';
import TailwindWrapper from './TailwindWrapper';

/**
 * Example component demonstrating how to use Tailwind CSS
 * within the isolated scope to prevent conflicts with regular CSS
 */
export const TailwindExample: React.FC = () => {
  return (
    <TailwindWrapper>
      {/* All Tailwind classes here will be prefixed with 'tw-' */}
      <div className="tw-bg-neutral-900 tw-text-neutral-100 tw-py-12 tw-px-6">
        <div className="tw-max-w-6xl tw-mx-auto">
          <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-8">
            
            {/* Hero Section */}
            <div className="tw-flex tw-flex-col tw-items-center tw-gap-4 tw-text-center">
              <div className="tw-border tw-border-neutral-700 tw-rounded-full tw-p-3 tw-bg-gradient-to-r tw-from-primary tw-to-secondary tw-bg-clip-text tw-text-transparent">
                <h3 className="font-montserrat">Earn 20% annual returns</h3>
              </div>
              
              <h1 className="font-montserrat tw-font-bold tw-text-3xl md:tw-text-5xl">
                <span className="tw-text-accent">Grow</span>
                <span className="tw-text-neutral-100"> Your Wealth. </span>
                <span className="tw-text-secondary">Get</span>
                <span className="tw-text-neutral-100"> Funded.</span>
              </h1>
              
              <p className="tw-text-neutral-300 tw-text-base tw-leading-6 tw-max-w-md font-montserrat">
                Stake your ESVC tokens and earn daily ROI. Unlock exclusive opportunities 
                to pitch your startup ideas for funding.
              </p>
              
              <div className="tw-flex tw-gap-4 tw-mt-6">
                <button className="tw-bg-gradient-primary tw-text-white tw-px-6 tw-py-3 tw-rounded-lg tw-font-semibold tw-transition-all tw-duration-300 hover:tw-scale-105">
                  Get Started
                </button>
                <button className="tw-border tw-border-neutral-700 tw-text-neutral-100 tw-px-6 tw-py-3 tw-rounded-lg tw-font-semibold tw-transition-all tw-duration-300 hover:tw-border-primary">
                  Learn More
                </button>
              </div>
            </div>
            
            {/* Feature Cards */}
            <div className="tw-space-y-4">
              <div className="tw-bg-neutral-800 tw-p-6 tw-rounded-lg tw-border tw-border-neutral-700">
                <h3 className="tw-text-xl tw-font-semibold tw-text-primary tw-mb-2">Daily ROI</h3>
                <p className="tw-text-neutral-300">Earn consistent returns on your staked ESVC tokens</p>
              </div>
              
              <div className="tw-bg-neutral-800 tw-p-6 tw-rounded-lg tw-border tw-border-neutral-700">
                <h3 className="tw-text-xl tw-font-semibold tw-text-secondary tw-mb-2">Get Funded</h3>
                <p className="tw-text-neutral-300">Pitch your startup ideas and secure funding</p>
              </div>
              
              <div className="tw-bg-neutral-800 tw-p-6 tw-rounded-lg tw-border tw-border-neutral-700">
                <h3 className="tw-text-xl tw-font-semibold tw-text-accent tw-mb-2">Trade Capital</h3>
                <p className="tw-text-neutral-300">Access trading opportunities with our platform</p>
              </div>
            </div>
            
          </div>
        </div>
      </div>
    </TailwindWrapper>
  );
};

export default TailwindExample;
