{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2022",
    "useDefineForClassFields": true,
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  },
  "include": ["src"],
  "exclude": [
    "src/landing/components/ComparisonSection.tsx",
    "src/landing/components/Footer.tsx",
    "src/landing/components/Header.tsx",
    "src/landing/components/HeroSection.tsx",
    "src/landing/components/TradeChallengeHero.tsx",
    "src/landing/components/TradeChallengeHowItWorks.tsx",
    "src/landing/components/TradeChallengeTracker.tsx",
    "src/landing/components/TreasuryDashboard.tsx",
    "src/landing/layout.tsx"
  ]
}
