/* CSS Modules for regular components - isolated from Tailwind */

/* These styles will be scoped automatically by CSS modules */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.btnPrimary {
  background: linear-gradient(135deg, var(--accent-orange), #ff6b5b);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btnPrimary:hover {
  background: linear-gradient(135deg, var(--accent-orange-hover), #e85a4f);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(232, 90, 79, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 8px 25px rgba(232, 90, 79, 0.4);
  }
  50% {
    box-shadow: 0 8px 35px rgba(232, 90, 79, 0.6);
  }
  100% {
    box-shadow: 0 8px 25px rgba(232, 90, 79, 0.4);
  }
}

.btnSecondary {
  background: transparent;
  color: var(--text-primary);
  padding: 12px 24px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btnSecondary:hover {
  border-color: var(--accent-orange);
  color: var(--accent-orange);
}

.card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
}

.card:hover {
  border-color: var(--accent-orange);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(191, 65, 41, 0.1);
}

.heading {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-primary);
}

.text {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: var(--text-secondary);
  line-height: 1.5;
}

.textMuted {
  color: var(--text-muted);
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .container {
    max-width: 100% !important;
    padding: 0 !important;
  }

  .btnPrimary,
  .btnSecondary {
    font-size: 16px !important;
    padding: 16px 24px !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
    min-height: 48px !important;
  }
}
