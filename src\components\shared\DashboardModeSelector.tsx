import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDemoState } from '../../context/DemoStateContext';
import '../../styles/components/shared/DashboardModeSelector.css';

// Import icons
import userIcon from '../../assets/overview.png';
import treasuryIcon from '../../assets/wallet-money.png';
import tradingIcon from '../../assets/chart.png';
import chevronDownIcon from '../../assets/arrow-down.png';

type DashboardMode = 'user' | 'treasury' | 'trading';

interface DashboardModeSelectorProps {
  className?: string;
}

const DashboardModeSelector: React.FC<DashboardModeSelectorProps> = ({ className = '' }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { dashboardMode, setDashboardMode } = useDemoState();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const dashboardModes = [
    {
      id: 'user' as DashboardMode,
      label: 'User',
      icon: userIcon,
      route: '/user-dashboard',
      description: 'Your staking dashboard'
    },
    {
      id: 'treasury' as DashboardMode,
      label: 'Treasury',
      icon: treasuryIcon,
      route: '/overview',
      description: 'Treasury analytics'
    },
    {
      id: 'trading' as DashboardMode,
      label: 'Trading',
      icon: tradingIcon,
      route: '/trading-dashboard',
      description: 'Trading performance'
    }
  ];

  // Determine current mode based on route
  const getCurrentMode = (): DashboardMode => {
    const path = location.pathname;
    if (path.startsWith('/user-dashboard')) return 'user';
    if (path === '/trading-dashboard') return 'trading';
    if (['/overview', '/live-reserve', '/daily-transactions', '/real-time-staking', '/startup-funding', '/roi-distribution', '/visual-analytics'].includes(path)) {
      return 'treasury';
    }
    return dashboardMode;
  };

  const currentMode = getCurrentMode();

  const handleModeChange = (mode: DashboardMode, route: string) => {
    setIsDropdownOpen(false); // Close dropdown after selection
    setDashboardMode(mode);
    navigate(route);
  };

  const currentModeData = dashboardModes.find(mode => mode.id === currentMode);

  return (
    <div className={`dashboard-mode-selector ${className}`}>
      {/* Desktop Version - Tabs */}
      <div className="mode-selector-tabs desktop-only">
        {dashboardModes.map((mode) => (
          <button
            key={mode.id}
            className={`mode-tab ${currentMode === mode.id ? 'active' : ''}`}
            onClick={() => handleModeChange(mode.id, mode.route)}
            title={mode.description}
          >
            <div className="mode-tab-icon">
              <img src={mode.icon} alt={mode.label} />
            </div>
            <span className="mode-tab-label">{mode.label}</span>
          </button>
        ))}
      </div>

      {/* Mobile Version - Dropdown */}
      <div className="mode-selector-dropdown mobile-only">
        <button
          className="dropdown-trigger"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        >
          <div className="dropdown-trigger-content">
            <div className="mode-tab-icon">
              <img src={currentModeData?.icon} alt={currentModeData?.label} />
            </div>
            <span className="mode-tab-label">{currentModeData?.label}</span>
          </div>
          <div className={`dropdown-arrow ${isDropdownOpen ? 'open' : ''}`}>
            <img src={chevronDownIcon} alt="Toggle" />
          </div>
        </button>

        {isDropdownOpen && (
          <div className="dropdown-menu">
            {dashboardModes.map((mode) => (
              <button
                key={mode.id}
                className={`dropdown-item ${currentMode === mode.id ? 'active' : ''}`}
                onClick={() => handleModeChange(mode.id, mode.route)}
              >
                <div className="mode-tab-icon">
                  <img src={mode.icon} alt={mode.label} />
                </div>
                <span className="mode-tab-label">{mode.label}</span>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardModeSelector;
