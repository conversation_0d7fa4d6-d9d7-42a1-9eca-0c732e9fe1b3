import { BrowserRouter as Router, Routes, Route, useLocation, Navigate } from 'react-router-dom';
import { useEffect } from 'react';
import './styles/App.css';
import { DemoStateProvider } from './context/DemoStateContext';
import Hero from './components/homepage/Hero';
import TreasuryDashboard from './components/treasury-dashboard/TreasuryDashboard';
import ComparisonTable from './components/homepage/ComparisonTable';
import FAQ from './components/homepage/FAQ';
import SignUp from './components/auth/SignUp';
import ForgotPassword from './components/auth/ForgotPassword';
import VerifyEmail from './components/auth/VerifyEmail';
import Overview from './components/treasury-dashboard/Overview';
import LiveReserve from './components/treasury-dashboard/LiveReserve';
import DailyTransactions from './components/treasury-dashboard/DailyTransactions';
import RealTimeStaking from './components/treasury-dashboard/RealTimeStaking';
import StartupFunding from './components/funding-dashboard/StartupFunding';
import ROIDistribution from './components/treasury-dashboard/ROIDistribution';
import VisualAnalytics from './components/treasury-dashboard/VisualAnalytics';
import TradeChallenge from './components/trading-dashboard/TradingDashboard';
import DashboardLayout from './components/layouts/DashboardLayout';
import UserOverview from './components/user-dashboard/UserOverview';
import MyStake from './components/staking/MyStake';
import UserTransactions from './components/user-dashboard/UserTransactions';
import GetFunding from './components/funding-dashboard/GetFunding';
import SecuritySettings from './components/auth/SecuritySettings';
import ChangePassword from './components/auth/ChangePassword';
import ResetPassword from './components/auth/ResetPassword';
import TradingDashboard from './components/trading-dashboard/TradingDashboard';
import TradingDashboardMain from './components/trading-dashboard/TradingDashboardMain';
import ContactUs from './components/homepage/ContactUs';

// Scroll to top component
const ScrollToTop = () => {
  const location = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location]);

  return null;
};

// Landing Page Component
const LandingPage = () => {
  return (
    <DashboardLayout className="landing-page">
      <Hero />
      <TreasuryDashboard />
      <ComparisonTable />
      <FAQ />
    </DashboardLayout>
  );
};

// Auth Page Component
const AuthPage = ({ children }: { children: React.ReactNode }) => {
  return (
    <DashboardLayout className="auth-page">
      <main className="auth-main">
        {children}
      </main>
    </DashboardLayout>
  );
};

function App() {
  return (
    <DemoStateProvider>
      <Router>
        <ScrollToTop />
        <div className="app">
          <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/signup" element={
            <AuthPage>
              <SignUp initialMode="signup" />
            </AuthPage>
          } />
          <Route path="/login" element={
            <AuthPage>
              <SignUp initialMode="login" />
            </AuthPage>
          } />
          <Route path="/forgot-password" element={
            <AuthPage>
              <ForgotPassword onBack={() => window.history.back()} />
            </AuthPage>
          } />
          <Route path="/verify-email" element={
            <AuthPage>
              <VerifyEmail />
            </AuthPage>
          } />
          <Route path="/overview" element={<Overview />} />
          <Route path="/live-reserve" element={<LiveReserve />} />
          <Route path="/daily-transactions" element={<DailyTransactions />} />
          <Route path="/real-time-staking" element={<RealTimeStaking />} />
          <Route path="/startup-funding" element={<StartupFunding />} />
          <Route path="/roi-distribution" element={<ROIDistribution />} />
          <Route path="/visual-analytics" element={<VisualAnalytics />} />
          <Route path="/trade-challenge" element={<TradeChallenge />} />
          <Route path="/trade-challenge-signup" element={<TradingDashboard />} />
          <Route path="/stake-esvc" element={<Navigate to="/user-dashboard/get-funding" replace />} />
          <Route path="/user-dashboard" element={<UserOverview />} />
          <Route path="/user-dashboard/my-stake" element={<MyStake />} />
          <Route path="/user-dashboard/transactions" element={<UserTransactions />} />
          <Route path="/user-dashboard/get-funding" element={<GetFunding />} />
          <Route path="/user-dashboard/security-settings" element={<SecuritySettings />} />
          <Route path="/user-dashboard/change-password" element={<ChangePassword />} />
          <Route path="/user-dashboard/reset-password" element={<ResetPassword />} />
          <Route path="/contact-us" element={<ContactUs />} />
          <Route path="/trading-dashboard" element={<TradingDashboard />} />
          <Route path="/trading-dashboard-main" element={<TradingDashboardMain />} />
        </Routes>
      </div>
    </Router>
    </DemoStateProvider>
  );
}

export default App;
