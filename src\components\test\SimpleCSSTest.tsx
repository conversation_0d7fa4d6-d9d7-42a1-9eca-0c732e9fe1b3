import React from 'react';
import TailwindWrapper from '../../landing/components/TailwindWrapper';
import styles from '../../styles/components.module.css';

/**
 * Simple CSS isolation test that doesn't depend on Next.js components
 */
export const SimpleCSSTest: React.FC = () => {
  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#1a1a1a', 
      color: '#F5F5F5',
      minHeight: '100vh',
      fontFamily: 'Montserrat, sans-serif'
    }}>
      <h1 style={{ 
        textAlign: 'center', 
        marginBottom: '30px',
        color: '#BF4129'
      }}>
        CSS Isolation Test - Simple Version
      </h1>
      
      {/* Test 1: Regular CSS Module */}
      <section style={{ marginBottom: '40px' }}>
        <h2 style={{ color: '#F0C369', marginBottom: '20px' }}>
          1. Regular CSS Module Test
        </h2>
        <div className={styles.container}>
          <div className={styles.card}>
            <h3 className={styles.heading}>Regular CSS Card</h3>
            <p className={styles.text}>
              This uses CSS modules. The styles should be scoped and not conflict with Tailwind.
            </p>
            <button className={styles.btnPrimary}>
              Regular CSS Button
            </button>
          </div>
        </div>
      </section>

      {/* Test 2: Tailwind CSS with Wrapper */}
      <section style={{ marginBottom: '40px' }}>
        <h2 style={{ color: '#F0C369', marginBottom: '20px' }}>
          2. Tailwind CSS with Scoped Wrapper
        </h2>
        <TailwindWrapper>
          <div className="tw-bg-neutral-800 tw-p-6 tw-rounded-lg tw-border tw-border-neutral-700 tw-max-w-md">
            <h3 className="tw-text-xl tw-font-semibold tw-text-primary tw-mb-4">
              Tailwind CSS Card
            </h3>
            <p className="tw-text-neutral-300 tw-mb-4">
              This uses Tailwind CSS with tw- prefix. All styles are scoped to prevent conflicts.
            </p>
            <button className="tw-bg-gradient-primary tw-text-white tw-px-4 tw-py-2 tw-rounded tw-font-semibold tw-transition-all tw-duration-300 hover:tw-scale-105">
              Tailwind Button
            </button>
          </div>
        </TailwindWrapper>
      </section>

      {/* Test 3: Side by Side Comparison */}
      <section>
        <h2 style={{ color: '#F0C369', marginBottom: '20px' }}>
          3. Side by Side Comparison
        </h2>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: '1fr 1fr', 
          gap: '20px',
          alignItems: 'start'
        }}>
          
          {/* Regular CSS */}
          <div>
            <h4 style={{ marginBottom: '10px', color: '#D4D4D4' }}>Regular CSS:</h4>
            <div className={styles.card}>
              <p className={styles.text}>
                Uses CSS modules with scoped class names. 
                Background should be dark gray with orange accent.
              </p>
              <button className={styles.btnSecondary} style={{ marginTop: '10px' }}>
                CSS Module Button
              </button>
            </div>
          </div>

          {/* Tailwind CSS */}
          <div>
            <h4 style={{ marginBottom: '10px', color: '#D4D4D4' }}>Tailwind CSS:</h4>
            <TailwindWrapper>
              <div className="tw-bg-neutral-800 tw-p-4 tw-rounded-lg tw-border tw-border-neutral-700">
                <p className="tw-text-neutral-300 tw-mb-3">
                  Uses Tailwind with tw- prefix and scoped wrapper.
                  Should have similar appearance but different implementation.
                </p>
                <button className="tw-border tw-border-neutral-600 tw-text-neutral-100 tw-px-4 tw-py-2 tw-rounded tw-transition-all tw-duration-300 hover:tw-border-primary">
                  Tailwind Button
                </button>
              </div>
            </TailwindWrapper>
          </div>
        </div>
      </section>

      {/* Test Results */}
      <section style={{ 
        marginTop: '40px', 
        padding: '20px', 
        backgroundColor: '#262626', 
        borderRadius: '8px' 
      }}>
        <h3 style={{ color: '#F0C369', marginBottom: '15px' }}>
          Expected Results:
        </h3>
        <ul style={{ color: '#D4D4D4', paddingLeft: '20px', lineHeight: '1.6' }}>
          <li>Both cards should have similar dark backgrounds</li>
          <li>Regular CSS buttons should have gradient backgrounds</li>
          <li>Tailwind buttons should have border styling</li>
          <li>No style conflicts or unexpected overrides</li>
          <li>Each system maintains its own styling integrity</li>
        </ul>
      </section>
    </div>
  );
};

export default SimpleCSSTest;
