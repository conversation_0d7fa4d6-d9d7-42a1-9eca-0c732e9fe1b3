import React from 'react';
import '../tailwind.css';

interface TailwindWrapperProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Wrapper component that provides Tailwind CSS scope isolation
 * All Tailwind classes inside this wrapper will be prefixed with 'tw-'
 * and scoped to the '.tailwind-scope' class to prevent conflicts
 */
export const TailwindWrapper: React.FC<TailwindWrapperProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={`tailwind-scope ${className}`}>
      {children}
    </div>
  );
};

export default TailwindWrapper;
