import Image from "next/image";
import Link from "next/link";

export default function TradeChallengeHero() {
  return (
    <section className="relative w-full">
      {/* Background blur effects */}
      <div className="absolute w-[239px] h-[239px] top-0 right-0 bg-[#d19049] rounded-[119.5px] blur-[100px] opacity-30"></div>
      <div className="absolute w-[239px] h-[239px] top-[232px] left-[22px] bg-[#cc6754] rounded-[119.5px] blur-[100px] opacity-30"></div>
      
      <section className="flex flex-col items-center gap-8 w-full max-w-4xl mx-auto mb-20 py-5 px-5 lg:px-0">
        <div className="flex flex-col items-center gap-4 w-full text-center relative">
          <div className="inline-flex items-center justify-center gap-2.5 px-4 rounded-full"></div>
          
          <div className="lg:flex font-montserrat font-bold md:text-[47px] text-[24px]">
            <h1 className="text-neutral-100">$1,000 </h1>
            <h1 className="text-[#d19049] rotate-90 lg:rotate-0 lg:px-5"> ➔</h1>
            <span className="text-neutral-100">1 Billion in 3,000 Trades</span>
          </div>
          
          <p className="max-w-[627px] font-montserrat font-normal text-neutral-300 text-base leading-6">
            Tap into automated trading powered by our founder-built trading bot. Connect your exchange, pay a one-time entry fee, and let the bot trade on your behalf. Profit-sharing is automatic. We only win when you do.
          </p>
          
          <Image
            className="top-[120px] w-[200px] h-[13px] absolute lg:top-[80px] lg:w-[300px] lg:right-100 lg:-translate-x-1/2"
            alt="Decorative line"
            src="/c.animaapp.com/mc62dpc6QTKBF1/img/vector-1.svg"
            width={300}
            height={13}
          />
        </div>
        
        <Link href="/login">
          <button className="whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 shadow h-14 font-montserrat font-semibold text-[#171717] text-lg bg-[#C6741B] hover:bg-[#B8681A] rounded-full px-10 py-2.5 flex items-center justify-center gap-3">
            Get the Bot - $100/Year
          </button>
        </Link>
      </section>
    </section>
  );
}
