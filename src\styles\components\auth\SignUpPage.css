/* SignUp Page Wrapper */
.signup-page-wrapper {
  min-height: 100vh;
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* Main content area */
.signup-main {
  flex: 1;
  position: relative;
  z-index: 1;
  padding-top: 120px; /* Account for fixed header */
}

/* Ensure header is above everything */
.signup-page-wrapper .header {
  z-index: 100;
}

/* Ensure footer is properly positioned */
.signup-page-wrapper .footer {
  z-index: 10;
  margin-top: auto;
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .signup-main {
    padding-top: 80px;
    padding-bottom: 20px;
  }

  .signup-page-wrapper .header {
    left: 16px;
    right: 16px;
    top: 16px;
  }
}
