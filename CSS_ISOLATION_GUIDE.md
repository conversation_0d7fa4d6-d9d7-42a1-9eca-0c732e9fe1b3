# CSS Isolation Guide

This guide explains how to use both Tailwind CSS and regular CSS in the same project without conflicts.

## Overview

The project is configured to support two isolated styling systems:

1. **Tailwind CSS** - For landing page components with prefix `tw-` and scoped to `.tailwind-scope`
2. **Regular CSS** - For dashboard and other components using CSS modules and traditional CSS

## Tailwind CSS Usage

### For Landing Page Components

1. **Wrap components with TailwindWrapper:**
```tsx
import TailwindWrapper from '../landing/components/TailwindWrapper';

export const MyLandingComponent = () => {
  return (
    <TailwindWrapper>
      {/* Use Tailwind classes with tw- prefix */}
      <div className="tw-bg-neutral-900 tw-text-white tw-p-4">
        <h1 className="tw-text-2xl tw-font-bold">Hello World</h1>
      </div>
    </TailwindWrapper>
  );
};
```

2. **All Tailwind classes must use the `tw-` prefix:**
   - `tw-bg-blue-500` instead of `bg-blue-500`
   - `tw-text-center` instead of `text-center`
   - `tw-flex tw-items-center` instead of `flex items-center`

3. **Custom classes are available without prefix inside TailwindWrapper:**
   - `font-montserrat`
   - `font-londrina-outline`
   - `bg-primary`, `bg-secondary`, `bg-accent`
   - `text-primary`, `text-secondary`, `text-accent`

### Configuration Files

- **tailwind.config.js** - Tailwind configuration with prefix and scoping
- **src/landing/tailwind.css** - Scoped Tailwind styles
- **postcss.config.js** - PostCSS configuration

## Regular CSS Usage

### For Dashboard and Other Components

1. **Use CSS Modules for component-specific styles:**
```tsx
import styles from './MyComponent.module.css';

export const MyComponent = () => {
  return (
    <div className={styles.container}>
      <button className={styles.btnPrimary}>Click me</button>
    </div>
  );
};
```

2. **Use global CSS for app-wide styles:**
```tsx
// Import global styles in main.tsx or component files
import './styles/index.css';
```

3. **Example CSS Module (MyComponent.module.css):**
```css
.container {
  max-width: 1200px;
  margin: 0 auto;
}

.btnPrimary {
  background: var(--accent-orange);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
}
```

### Available CSS Variables

Global CSS variables are defined in `src/styles/index.css`:
- `--bg-primary`, `--bg-secondary`, `--bg-card`
- `--text-primary`, `--text-secondary`, `--text-muted`
- `--accent-orange`, `--accent-gold`
- `--border-color`, `--border-secondary`

## File Structure

```
src/
├── landing/
│   ├── tailwind.css          # Scoped Tailwind styles
│   ├── globals.css           # Legacy file (deprecated)
│   └── components/
│       ├── TailwindWrapper.tsx
│       └── TailwindExample.tsx
├── styles/
│   ├── index.css             # Global CSS variables and base styles
│   ├── App.css               # App-specific styles
│   └── components.module.css # Example CSS module
└── components/
    └── shared/
        └── RegularCSSExample.tsx
```

## Best Practices

### Do's ✅

1. **Use TailwindWrapper for all landing page components**
2. **Always prefix Tailwind classes with `tw-`**
3. **Use CSS modules for component-specific regular CSS**
4. **Use global CSS variables for consistent theming**
5. **Keep Tailwind usage limited to landing page directory**

### Don'ts ❌

1. **Don't use unprefixed Tailwind classes outside TailwindWrapper**
2. **Don't import Tailwind CSS globally**
3. **Don't mix Tailwind and regular CSS in the same component**
4. **Don't use `!important` unless absolutely necessary**

## Troubleshooting

### Tailwind Classes Not Working
- Ensure component is wrapped with `TailwindWrapper`
- Check that classes use `tw-` prefix
- Verify the component file is in the Tailwind content paths

### CSS Conflicts
- Use CSS modules for component-specific styles
- Ensure Tailwind is properly scoped
- Check CSS specificity and use CSS variables

### Build Issues
- Verify PostCSS configuration is correct
- Check that Tailwind config paths are accurate
- Ensure all imports are properly resolved

## Migration Guide

### Converting Existing Components to Use Isolation

1. **For landing page components:**
   - Wrap with `TailwindWrapper`
   - Add `tw-` prefix to all Tailwind classes
   - Remove global Tailwind imports

2. **For regular components:**
   - Convert CSS to CSS modules
   - Use global CSS variables
   - Import styles as modules

This isolation strategy ensures that both styling systems can coexist without conflicts while maintaining the flexibility to use the best tool for each use case.
