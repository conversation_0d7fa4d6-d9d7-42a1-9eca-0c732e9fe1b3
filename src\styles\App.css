.app {
  min-height: 100vh;
  color: #FFFFFF;
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  position: relative;
}

/* Landing Page */
.landing-page {
  min-height: 100vh;
}

.auth-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.auth-main {
  flex: 1;
  position: relative;
  z-index: 1;
  padding-top: 40px; 
}

.app > * {
  position: relative;
  z-index: 1;
}

.app .header {
  z-index: 100;
}

@media (max-width: 768px) {
  .auth-main {
    padding-top: 60px;
  }
}
