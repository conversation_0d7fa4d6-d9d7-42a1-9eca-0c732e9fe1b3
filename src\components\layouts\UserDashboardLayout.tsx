import React from 'react';
import { useNavigate } from 'react-router-dom';
import UserDashboardHeader from './UserDashboardHeader';
import Footer from '../homepage/Footer';
import '../../styles/components/user-dashboard/UserDashboardLayout.css';

interface UserDashboardLayoutProps {
  children: React.ReactNode;
  showBlurGradients?: boolean;
  className?: string;
  hideNavAndFooter?: boolean;
}

const UserDashboardLayout: React.FC<UserDashboardLayoutProps> = ({
  children,
  showBlurGradients = true,
  className = '',
  hideNavAndFooter = false
}) => {
  const navigate = useNavigate();

  return (
    <div className={`user-dashboard-layout-container ${className}`}>
      {showBlurGradients && (
        <>
          <div className="blur-gradient blur-gradient-1"></div>
          <div className="blur-gradient blur-gradient-2"></div>
          <div className="blur-gradient blur-gradient-3"></div>
          <div className="blur-gradient blur-gradient-4"></div>
          <div className="blur-gradient blur-gradient-5"></div>
        </>
      )}

      {!hideNavAndFooter && (
        <UserDashboardHeader
          onNavigateToLanding={() => navigate('/')}
        />
      )}

      <main className="user-dashboard-main">
        {children}
      </main>

      {!hideNavAndFooter && <Footer />}
    </div>
  );
};

export default UserDashboardLayout;
