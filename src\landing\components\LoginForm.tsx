"use client";

import { useState } from "react";

export default function LoginForm() {
  const [activeTab, setActiveTab] = useState("login");

  return (
    <div className="w-full min-h-screen bg-neutral-900">
      <div className="relative mx-auto max-w-[1440px] py-6">
        {/* Background blur effects */}
        <div className="absolute lg:w-[239px] lg:h-[239px] lg:top-[280px] lg:right-0 bg-[#d19049] rounded-[119.5px] blur-[100px] opacity-30"></div>
        <div className="absolute top-[-70px]">
          <svg xmlns="http://www.w3.org/2000/svg" width="204" height="306" viewBox="0 0 204 306" fill="none">
            <g opacity="0.3" filter="url(#filter0_f_644_12575)">
              <circle cx="51" cy="153" r="57" fill="#CC6754"></circle>
            </g>
            <defs>
              <filter id="filter0_f_644_12575" x="-101.397" y="0.602509" width="304.795" height="304.795" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend>
                <feGaussianBlur stdDeviation="47.6987" result="effect1_foregroundBlur_644_12575"></feGaussianBlur>
              </filter>
            </defs>
          </svg>
        </div>
        <div className="absolute left-[195px] top-90 rounded-full">
          <svg xmlns="http://www.w3.org/2000/svg" width="231" height="344" viewBox="0 0 231 344" fill="none">
            <g opacity="0.3" filter="url(#filter0_f_644_12578)">
              <circle cx="172" cy="172" r="64" fill="#D19049"></circle>
            </g>
            <defs>
              <filter id="filter0_f_644_12578" x="0.887032" y="0.887032" width="342.226" height="342.226" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend>
                <feGaussianBlur stdDeviation="53.5565" result="effect1_foregroundBlur_644_12578"></feGaussianBlur>
              </filter>
            </defs>
          </svg>
        </div>

        <div className="flex flex-col items-center mt-[35px] px-5">
          <h1 className="font-montserrat font-semibold text-neutral-100 text-[40px] text-center">
            Welcome to ESVC Capital
          </h1>
          
          <div className="lg:w-[480px] mt-[30px]">
            {/* Tab Navigation */}
            <div className="inline-flex h-9 items-center justify-center text-muted-foreground w-full py-6 border-[0.2px] rounded-lg bg-neutral-900 border-neutral-700">
              <button
                type="button"
                onClick={() => setActiveTab("signup")}
                className={`inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 flex-1 h-12 font-montserrat text-lg ${
                  activeTab === "signup"
                    ? "bg-neutral-700 text-white font-semibold shadow"
                    : "text-white font-normal"
                }`}
              >
                Sign Up
              </button>
              <button
                type="button"
                onClick={() => setActiveTab("login")}
                className={`inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 flex-1 h-12 font-montserrat text-lg ${
                  activeTab === "login"
                    ? "bg-neutral-700 text-white font-semibold shadow"
                    : "text-white font-normal"
                }`}
              >
                Login
              </button>
            </div>

            {/* Tab Content */}
            <div className="mt-[30px]">
              {activeTab === "login" && (
                <div className="flex items-center justify-center bg-dark p-4">
                  <div className="bg-darker rounded-lg shadow-xl w-full max-w-md">
                    <h2 className="font-montserrat font-semibold text-neutral-100 text-xl text-center mb-8">
                      Create Your Account to Start Earning
                    </h2>
                    <form className="space-y-6">
                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-neutral-300 mb-2">
                          Email Address
                        </label>
                        <div className="relative">
                          <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-neutral-500">
                              <path d="m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7"></path>
                              <rect x="2" y="4" width="20" height="16" rx="2"></rect>
                            </svg>
                          </span>
                          <input
                            id="email"
                            type="email"
                            className="w-full pl-10 pr-3 py-2 bg-neutral-800 rounded-full text-neutral-100 placeholder-neutral-500 focus:ring-primary focus:border-primary border border-neutral-700"
                            placeholder="<EMAIL>"
                            name="email"
                          />
                          <input type="hidden" name="platform" value="esvc" />
                        </div>
                      </div>
                      <div>
                        <label htmlFor="password" className="block text-sm font-medium text-neutral-300 mb-2">
                          Create Password
                        </label>
                        <div className="relative">
                          <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-neutral-500">
                              <rect width="18" height="11" x="3" y="11" rx="2" ry="2"></rect>
                              <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                            </svg>
                          </span>
                          <input
                            id="password"
                            type="password"
                            className="w-full pl-10 pr-3 py-2 bg-neutral-800 rounded-full text-neutral-100 placeholder-neutral-500 focus:ring-primary focus:border-primary border border-neutral-700"
                            placeholder="Create a strong password"
                            name="password"
                          />
                        </div>
                      </div>
                      <button
                        type="submit"
                        className="w-full h-12 mt-4 bg-[#bf4129] hover:bg-[#a83a25] rounded-full font-montserrat font-semibold text-neutral-50"
                      >
                        Login
                      </button>
                    </form>
                  </div>
                </div>
              )}

              {activeTab === "signup" && (
                <div className="flex items-center justify-center bg-dark p-4">
                  <div className="bg-darker rounded-lg shadow-xl w-full max-w-md">
                    <h2 className="font-montserrat font-semibold text-neutral-100 text-xl text-center mb-8">
                      Create Your Account to Start Earning
                    </h2>
                    <form className="space-y-6">
                      <div>
                        <label htmlFor="signup-email" className="block text-sm font-medium text-neutral-300 mb-2">
                          Email Address
                        </label>
                        <div className="relative">
                          <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-neutral-500">
                              <path d="m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7"></path>
                              <rect x="2" y="4" width="20" height="16" rx="2"></rect>
                            </svg>
                          </span>
                          <input
                            id="signup-email"
                            type="email"
                            className="w-full pl-10 pr-3 py-2 bg-neutral-800 rounded-full text-neutral-100 placeholder-neutral-500 focus:ring-primary focus:border-primary border border-neutral-700"
                            placeholder="<EMAIL>"
                            name="email"
                          />
                        </div>
                      </div>
                      <div>
                        <label htmlFor="signup-password" className="block text-sm font-medium text-neutral-300 mb-2">
                          Create Password
                        </label>
                        <div className="relative">
                          <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-neutral-500">
                              <rect width="18" height="11" x="3" y="11" rx="2" ry="2"></rect>
                              <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                            </svg>
                          </span>
                          <input
                            id="signup-password"
                            type="password"
                            className="w-full pl-10 pr-3 py-2 bg-neutral-800 rounded-full text-neutral-100 placeholder-neutral-500 focus:ring-primary focus:border-primary border border-neutral-700"
                            placeholder="Create a strong password"
                            name="password"
                          />
                        </div>
                      </div>
                      <div>
                        <label htmlFor="confirm-password" className="block text-sm font-medium text-neutral-300 mb-2">
                          Confirm Password
                        </label>
                        <div className="relative">
                          <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-neutral-500">
                              <rect width="18" height="11" x="3" y="11" rx="2" ry="2"></rect>
                              <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                            </svg>
                          </span>
                          <input
                            id="confirm-password"
                            type="password"
                            className="w-full pl-10 pr-3 py-2 bg-neutral-800 rounded-full text-neutral-100 placeholder-neutral-500 focus:ring-primary focus:border-primary border border-neutral-700"
                            placeholder="Confirm your password"
                            name="confirmPassword"
                          />
                        </div>
                      </div>
                      <button
                        type="submit"
                        className="w-full h-12 mt-4 bg-[#bf4129] hover:bg-[#a83a25] rounded-full font-montserrat font-semibold text-neutral-50"
                      >
                        Sign Up
                      </button>
                    </form>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
