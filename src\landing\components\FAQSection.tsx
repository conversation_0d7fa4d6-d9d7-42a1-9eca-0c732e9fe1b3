"use client";

import { useState } from "react";

interface FAQItem {
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    question: "What is ESVC?",
    answer: "ESVC is a decentralized staking protocol that allows users to earn rewards by staking their ESVC tokens. It leverages the Solana blockchain for high-speed and low-cost transactions."
  },
  {
    question: "How do I stake ESVC?",
    answer: "Simply click \"Start Staking Now\", create an account, choose how much you want to stake (in USD), and deposit using either Solana (SOL) or USDC. Your ESVC tokens will be automatically purchased and staked for a 6 or 12-month period."
  },
  {
    question: "What's the minimum amount I can stake?",
    answer: "The minimum staking amount is 50 USD to ensure efficient transaction processing and reward distribution within the protocol."
  },
  {
    question: "How much ROI can I earn?",
    answer: "Expected ROI varies based on staking period and current network conditions. Typically, annual returns range from 8% to 15%. Detailed projections are available after you log in."
  },
  {
    question: "When can I withdraw my ROI?",
    answer: "Your earned ROI can be withdrawn at the end of your chosen staking period (6 or 12 months). Partial withdrawals during the staking period are not allowed to maintain network stability."
  }
];

export default function FAQSection() {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  return (
    <section className="relative w-full">
      <section className="min-h-screen flex flex-col items-center justify-center bg-dark p-4 sm:p-6 lg:p-8 relative w-full bg-[#1d1104] py-20 overflow-hidden">
        {/* Background blur effects */}
        <div className="absolute w-[234px] h-[234px] top-[141px] lg:left-1/2 -translate-x-1/2 bg-[#d19049] rounded-[117px] blur-[97.91px] opacity-20"></div>
        <div className="absolute w-[239px] h-[239px] top-0 lg:right-[-120px] rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20"></div>
        <div className="absolute w-[79px] h-[79px] bottom-[80px] lg:right-[-40px] rounded-[39.5px] blur-[33.05px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20"></div>
        <div className="absolute w-[239px] h-[239px] bottom-[40px] lg:left-[-120px] rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20"></div>
        
        <div className="w-full max-w-2xl">
          <h2 className="text-center font-montserrat font-semibold text-neutral-100 text-[40px] mb-12">
            Frequently Asked Questions
          </h2>
          
          <div className="space-y-4">
            {faqData.map((item, index) => (
              <div key={index} className="border border-neutral-700 rounded-lg overflow-hidden mb-4 bg-[#281705]">
                <button 
                  className="w-full text-left p-2 flex justify-between items-center text-neutral-100"
                  aria-expanded={openItems.includes(index)}
                  onClick={() => toggleItem(index)}
                >
                  <span className="text-lg font-semibold pr-4 text-white">
                    {index + 1}. {item.question}
                  </span>
                  <span className="flex-shrink-0 border rounded-lg p-1.5 border-neutral-700">
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      width="24" 
                      height="24" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      className={`w-6 h-6 text-neutral-700 transition-transform duration-200 ${openItems.includes(index) ? 'rotate-45' : ''}`}
                      aria-hidden="true"
                    >
                      <path d="M5 12h14"></path>
                      <path d="M12 5v14"></path>
                    </svg>
                  </span>
                </button>
                <div 
                  className={`grid transition-all duration-300 ease-in-out ${
                    openItems.includes(index) 
                      ? 'grid-rows-[1fr] opacity-100' 
                      : 'grid-rows-[0fr] opacity-0'
                  }`}
                >
                  <div className="overflow-hidden">
                    <div className="p-5 pt-0 text-textMuted leading-relaxed text-[#d4d4d4]">
                      {item.answer}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mt-10">
            <button className="flex items-center bg-[#bf4129] hover:bg-[#a83a25] text-white py-3 px-6 rounded-full font-semibold text-lg transition-colors duration-200 shadow-lg">
              Start Staking Now 
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-rocket ml-2 w-5 h-5" aria-hidden="true">
                <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"></path>
                <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"></path>
                <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"></path>
                <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"></path>
              </svg>
            </button>
            <button className="bg-transparent text-textLight border border-textLight py-3 px-6 rounded-full font-semibold text-lg hover:border-primary hover:text-primary transition-colors duration-200">
              See How It Works
            </button>
          </div>
        </div>
      </section>
    </section>
  );
}
