/* Trading Dashboard - Main Container */
.trading-dashboard {
  min-height: 100vh;
  background: #1A1A1A;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  padding: 120px 40px 40px;
  position: relative;
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 40px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.go-back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.go-back-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.dashboard-main-title {
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
}

/* Progress Bar */
.progress-container {
  max-width: 1400px;
  margin: 0 auto 40px;
}

.progress-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
  padding: 24px;
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(20px);
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  position: relative;
}

.progress-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20px;
  right: -40px;
  width: 40px;
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
}

.progress-step.completed:not(:last-child)::after {
  background: #BF4129;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  transition: all 0.3s ease;
}

.progress-step.completed .step-number {
  background: #BF4129;
  border-color: #BF4129;
}

.progress-step.active .step-number {
  background: #BF4129;
  border-color: #BF4129;
  box-shadow: 0 0 20px rgba(191, 65, 41, 0.4);
}

.step-label {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  max-width: 120px;
}

.progress-step.completed .step-label,
.progress-step.active .step-label {
  color: #FFFFFF;
}

/* Success Alert */
.success-alert {
  max-width: 1400px;
  margin: 0 auto 40px;
}

.alert-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px 24px;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 12px;
  position: relative;
}

.alert-icon {
  width: 40px;
  height: 40px;
  background: #22C55E;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #FFFFFF;
  flex-shrink: 0;
}

.alert-text h4 {
  font-size: 16px;
  font-weight: 600;
  color: #22C55E;
  margin: 0 0 8px 0;
}

.alert-text p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.5;
}

.alert-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  transition: color 0.3s ease;
}

.alert-close:hover {
  color: #FFFFFF;
}

/* Dashboard Content */
.dashboard-content {
  max-width: 1400px;
  margin: 0 auto;
}

/* Dashboard Stats */
.dashboard-stats {
  margin-bottom: 60px;
}

.dashboard-title {
  font-size: 28px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 32px 0;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.stat-card {
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
  min-height: auto;
}

.stat-card:hover {
  transform: translateY(-2px);
  border-color: rgba(191, 65, 41, 0.3);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.stat-header {
  margin-bottom: 8px;
}

.stat-label {
  font-size: 11px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  letter-spacing: 0.5px;
  text-transform: uppercase;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-change.positive {
  color: #22C55E;
}

.stat-change.negative {
  color: #EF4444;
}

/* More Than Trading Section */
.more-than-trading {
  background: linear-gradient(135deg, rgba(191, 65, 41, 0.1) 0%, rgba(209, 144, 73, 0.1) 100%);
  border: 1px solid rgba(191, 65, 41, 0.2);
  border-radius: 24px;
  padding: 48px;
  text-align: center;
  max-width: 1400px;
  margin: 0 auto;
}

.more-than-trading-title {
  font-size: 36px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 24px 0;
  background: linear-gradient(135deg, #BF4129 0%, #D19049 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.more-than-trading-text {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto 40px;
}

.more-than-trading-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.start-staking-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #BF4129 0%, #D19049 100%);
  border: none;
  border-radius: 8px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(191, 65, 41, 0.3);
}

.start-staking-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(191, 65, 41, 0.4);
}

.get-funded-btn {
  padding: 16px 32px;
  background: transparent;
  border: 2px solid #BF4129;
  border-radius: 12px;
  color: #BF4129;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.get-funded-btn:hover {
  background: #BF4129;
  color: #FFFFFF;
  transform: translateY(-2px);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .trading-dashboard {
    padding: 100px 20px 20px;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .dashboard-main-title {
    font-size: 24px;
  }

  .progress-bar {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
  }

  .progress-step:not(:last-child)::after {
    display: none;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-card {
    padding: 20px;
  }

  .stat-value {
    font-size: 24px;
  }

  .more-than-trading {
    padding: 32px 24px;
  }

  .more-than-trading-title {
    font-size: 28px;
  }

  .more-than-trading-buttons {
    flex-direction: column;
    align-items: center;
  }

  .start-staking-btn,
  .get-funded-btn {
    width: 100%;
    max-width: 280px;
  }

  /* Mobile Custom Dropdown Optimization */
  .custom-dropdown {
    width: 100%;
  }

  .dropdown-selected {
    padding: 14px 16px;
    font-size: 16px;
    min-height: 48px;
    box-sizing: border-box;
  }

  .dropdown-arrow {
    font-size: 16px;
  }

  .dropdown-option {
    padding: 14px 16px;
    font-size: 16px;
    min-height: 48px;
    display: flex;
    align-items: center;
  }

  .dropdown-options {
    max-height: 60vh;
    border-radius: 0 0 8px 8px;
  }

  /* Dashboard Main Mobile Styles */
  .dashboard-main {
    padding: 2px 16px 16px;
  }

  .dashboard-main .dashboard-cards {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .dashboard-main .dashboard-card {
    padding: 12px;
    min-height: 80px;
  }

  .dashboard-main .dashboard-card > div:first-child {
    font-size: 10px;
    margin-bottom: 4px;
  }

  .dashboard-main .dashboard-card > div:nth-child(2) {
    font-size: 18px;
    margin-bottom: 3px;
  }

  .dashboard-main .dashboard-card .dashboard-profit,
  .dashboard-main .dashboard-card .dashboard-loss {
    font-size: 11px;
  }

  .dashboard-success-alert {
    padding: 12px 16px;
    margin-bottom: 12px;
    margin-top: 0;
  }

  .dashboard-success-alert span:first-child {
    width: 28px;
    height: 28px;
    min-width: 28px;
    min-height: 28px;
    font-size: 14px;
    line-height: 1;
  }

  .dashboard-success-desc {
    font-size: 13px;
  }
}

/* Custom Dropdown Styles for Trading Dashboard */
.custom-dropdown {
  position: relative;
  width: 100%;
}

.dropdown-selected {
  width: 100%;
  padding: 16px 20px;
  background: rgba(38, 38, 38, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
}

.dropdown-selected:hover {
  background: rgba(48, 48, 48, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.dropdown-arrow {
  font-size: 14px;
  transition: transform 0.3s ease;
  opacity: 0.7;
  flex-shrink: 0;
}

.dropdown-options {
  position: absolute;
  top: calc(100% - 1px);
  left: 0;
  right: 0;
  background: rgba(38, 38, 38, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(191, 65, 41, 0.6);
  border-top: none;
  border-radius: 0 0 12px 12px;
  overflow: hidden;
  z-index: 9999;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  max-height: 0;
  opacity: 0;
  transform: translateY(-8px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.dropdown-options.open {
  max-height: 240px;
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

.dropdown-options.open ~ .dropdown-selected {
  border-color: rgba(191, 65, 41, 0.6);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background: rgba(48, 48, 48, 0.95);
}

.dropdown-options.open ~ .dropdown-selected .dropdown-arrow {
  transform: rotate(180deg);
  opacity: 1;
}

.dropdown-option {
  padding: 16px 20px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dropdown-option:last-child {
  border-bottom: none;
}

.dropdown-option:hover {
  background: rgba(255, 255, 255, 0.08);
  color: #FFFFFF;
}

.dropdown-option.selected {
  background: rgba(191, 65, 41, 0.15);
  color: #FFFFFF;
  font-weight: 600;
}

.dropdown-option.selected:hover {
  background: rgba(191, 65, 41, 0.25);
}

/* Dashboard Main Section */
.dashboard-main {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.dashboard-success-alert {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 12px;
  padding: 16px 20px;
  margin-bottom: 16px;
  margin-top: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #22C55E;
}

.dashboard-success-alert span:first-child {
  width: 32px;
  height: 32px;
  min-width: 32px;
  min-height: 32px;
  background: #22C55E;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
  flex-shrink: 0;
  box-sizing: border-box;
  text-align: center;
  line-height: 1;
}

.dashboard-success-desc {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 4px;
}

/* Dashboard Cards Grid for Trading */
.dashboard-main .dashboard-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-top: 20px;
}

.dashboard-main .dashboard-card {
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
  min-height: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: left;
}

.dashboard-main .dashboard-card:hover {
  transform: translateY(-2px);
  border-color: rgba(191, 65, 41, 0.3);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.dashboard-main .dashboard-card > div:first-child {
  font-size: 11px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 6px;
}

.dashboard-main .dashboard-card > div:nth-child(2) {
  font-size: 20px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 4px;
}

.dashboard-main .dashboard-card .dashboard-profit {
  color: #4ade80;
  font-size: 12px;
  font-weight: 500;
}

.dashboard-main .dashboard-card .dashboard-loss {
  color: #f87171;
  font-size: 12px;
  font-weight: 500;
}

