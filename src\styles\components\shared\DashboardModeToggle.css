/* Dashboard Mode Toggle */
.dashboard-mode-toggle {
  position: fixed;
  top: 120px;
  right: 40px;
  z-index: 999;
  font-family: 'Montserrat', sans-serif;
}

.mode-toggle-container {
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  min-width: 320px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.mode-toggle-header {
  margin-bottom: 20px;
  text-align: center;
}

.mode-toggle-title {
  font-size: 18px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 8px 0;
}

.mode-toggle-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.mode-toggle-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.mode-option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mode-option:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(191, 65, 41, 0.3);
  transform: translateY(-2px);
}

.mode-option.active {
  background: linear-gradient(135deg, rgba(191, 65, 41, 0.2) 0%, rgba(209, 144, 73, 0.2) 100%);
  border-color: #BF4129;
  box-shadow: 0 4px 16px rgba(191, 65, 41, 0.3);
}

.mode-option.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(191, 65, 41, 0.1) 0%, rgba(209, 144, 73, 0.1) 100%);
  z-index: -1;
}

.mode-option-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  flex-shrink: 0;
}

.mode-option.active .mode-option-icon {
  background: rgba(191, 65, 41, 0.3);
}

.mode-option-icon img {
  width: 20px;
  height: 20px;
  object-fit: contain;
  filter: brightness(0) invert(1);
}

.mode-option.active .mode-option-icon img {
  filter: brightness(0) invert(1);
}

.mode-option-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.mode-option-label {
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
}

.mode-option-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.3;
}

.mode-option.active .mode-option-description {
  color: rgba(255, 255, 255, 0.8);
}

.mode-option-indicator {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.active-indicator {
  width: 8px;
  height: 8px;
  background: #BF4129;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(191, 65, 41, 0.6);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 8px rgba(191, 65, 41, 0.6);
  }
  50% {
    box-shadow: 0 0 16px rgba(191, 65, 41, 0.8);
  }
  100% {
    box-shadow: 0 0 8px rgba(191, 65, 41, 0.6);
  }
}

.mode-toggle-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

.current-mode-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-mode-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.current-mode-value {
  font-size: 12px;
  font-weight: 600;
  color: #BF4129;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .dashboard-mode-toggle {
    position: static;
    top: auto;
    right: auto;
    margin: 20px;
    order: -1;
  }

  .mode-toggle-container {
    min-width: auto;
    width: 100%;
    padding: 20px;
  }

  .mode-toggle-options {
    gap: 8px;
  }

  .mode-option {
    padding: 12px;
    gap: 12px;
  }

  .mode-option-icon {
    width: 32px;
    height: 32px;
  }

  .mode-option-icon img {
    width: 16px;
    height: 16px;
  }

  .mode-option-label {
    font-size: 13px;
  }

  .mode-option-description {
    font-size: 11px;
  }
}

/* Compact Mode for Smaller Screens */
@media (max-width: 1200px) {
  .dashboard-mode-toggle {
    right: 20px;
  }

  .mode-toggle-container {
    min-width: 280px;
    padding: 20px;
  }
}

/* Hide on very small screens when in header */
@media (max-width: 480px) {
  .dashboard-mode-toggle.header-toggle {
    display: none;
  }
}
