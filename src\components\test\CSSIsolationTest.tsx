import React from 'react';
import TailwindWrapper from '../../landing/components/TailwindWrapper';
import RegularCSSExample from '../shared/RegularCSSExample';
import styles from '../../styles/components.module.css';

/**
 * Test component to verify CSS isolation between Tailwind and regular CSS
 * This component demonstrates both styling systems working side by side
 */
export const CSSIsolationTest: React.FC = () => {
  return (
    <div style={{ padding: '20px', backgroundColor: 'var(--bg-primary)' }}>
      <h1 style={{ color: 'var(--text-primary)', marginBottom: '30px', textAlign: 'center' }}>
        CSS Isolation Test Page
      </h1>
      
      {/* Section 1: Regular CSS with CSS Modules */}
      <section style={{ marginBottom: '40px' }}>
        <h2 style={{ color: 'var(--accent-orange)', marginBottom: '20px' }}>
          1. Regular CSS with CSS Modules
        </h2>
        <div className={styles.container}>
          <RegularCSSExample
            title="Regular CSS Component"
            description="This component uses CSS modules and regular CSS variables. It should not be affected by Tailwind styles."
            onButtonClick={() => alert('Regular CSS button clicked!')}
          />
        </div>
      </section>

      {/* Section 2: Tailwind CSS with Scoped Wrapper */}
      <section style={{ marginBottom: '40px' }}>
        <h2 style={{ color: 'var(--accent-orange)', marginBottom: '20px' }}>
          2. Tailwind CSS with Scoped Wrapper
        </h2>
        <TailwindWrapper>
          <div className="tw-bg-neutral-800 tw-p-6 tw-rounded-lg tw-border tw-border-neutral-700">
            <h3 className="tw-text-xl tw-font-semibold tw-text-primary tw-mb-4">
              Tailwind CSS Component
            </h3>
            <p className="tw-text-neutral-300 tw-mb-4">
              This component uses Tailwind CSS with the tw- prefix. All styles are scoped 
              to prevent conflicts with regular CSS.
            </p>
            <div className="tw-flex tw-gap-4">
              <button 
                className="tw-bg-gradient-primary tw-text-white tw-px-4 tw-py-2 tw-rounded tw-font-semibold tw-transition-all tw-duration-300 hover:tw-scale-105"
                onClick={() => alert('Tailwind button clicked!')}
              >
                Tailwind Primary
              </button>
              <button className="tw-border tw-border-neutral-600 tw-text-neutral-100 tw-px-4 tw-py-2 tw-rounded tw-font-semibold tw-transition-all tw-duration-300 hover:tw-border-primary">
                Tailwind Secondary
              </button>
            </div>
          </div>
        </TailwindWrapper>
      </section>

      {/* Section 3: Mixed Layout Test */}
      <section style={{ marginBottom: '40px' }}>
        <h2 style={{ color: 'var(--accent-orange)', marginBottom: '20px' }}>
          3. Mixed Layout Test
        </h2>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
          
          {/* Left: Regular CSS */}
          <div className={styles.card}>
            <h3 className={styles.heading}>Regular CSS Card</h3>
            <p className={styles.text}>
              This card uses regular CSS modules. Notice how the styling is completely 
              independent from the Tailwind card next to it.
            </p>
            <button className={styles.btnPrimary} style={{ marginTop: '12px' }}>
              Regular Button
            </button>
          </div>

          {/* Right: Tailwind CSS */}
          <TailwindWrapper>
            <div className="tw-bg-neutral-800 tw-p-6 tw-rounded-lg tw-border tw-border-neutral-700">
              <h3 className="tw-text-xl tw-font-semibold tw-text-secondary tw-mb-2">
                Tailwind CSS Card
              </h3>
              <p className="tw-text-neutral-300 tw-mb-4">
                This card uses Tailwind CSS with proper scoping. Both cards can coexist 
                without any style conflicts.
              </p>
              <button className="tw-bg-secondary tw-text-white tw-px-4 tw-py-2 tw-rounded tw-font-semibold tw-transition-all tw-duration-300 hover:tw-bg-primary">
                Tailwind Button
              </button>
            </div>
          </TailwindWrapper>
        </div>
      </section>

      {/* Section 4: Conflict Test */}
      <section>
        <h2 style={{ color: 'var(--accent-orange)', marginBottom: '20px' }}>
          4. Conflict Prevention Test
        </h2>
        <div style={{ backgroundColor: 'var(--bg-secondary)', padding: '20px', borderRadius: '8px' }}>
          <p style={{ color: 'var(--text-secondary)', marginBottom: '16px' }}>
            This section tests that styles don't leak between systems:
          </p>
          <ul style={{ color: 'var(--text-muted)', paddingLeft: '20px' }}>
            <li>Regular CSS buttons should maintain their gradient styling</li>
            <li>Tailwind buttons should maintain their scoped styling</li>
            <li>Font families should be consistent within each system</li>
            <li>Color variables should work independently</li>
            <li>No unexpected style overrides should occur</li>
          </ul>
        </div>
      </section>
    </div>
  );
};

export default CSSIsolationTest;
