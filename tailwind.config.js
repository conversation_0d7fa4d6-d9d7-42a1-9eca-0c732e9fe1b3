/** @type {import('tailwindcss').Config} */
export default {
  // Use a prefix to avoid conflicts with regular CSS
  prefix: 'tw-',

  // Only scan specific directories for Tailwind classes
  content: [
    "./src/landing/**/*.{js,ts,jsx,tsx}",
    "./src/components/landing/**/*.{js,ts,jsx,tsx}",
    // Add any other directories that should use Tailwind
    "./index.html",
  ],

  // Enable important to override regular CSS when needed
  important: '.tailwind-scope',
  
  theme: {
    extend: {
      fontFamily: {
        'montserrat': ['Montserrat', 'sans-serif'],
        'londrina-outline': ['Londrina Outline', 'sans-serif'],
      },
      colors: {
        // Custom colors from your landing page
        'primary': '#bf4129',
        'primary-hover': '#a83a25',
        'secondary': '#d19049',
        'accent': '#cc6754',
        'neutral': {
          50: 'oklch(98.5% 0 0)',
          100: 'oklch(97% 0 0)',
          300: 'oklch(87% 0 0)',
          500: 'oklch(55.6% 0 0)',
          600: 'oklch(43.9% 0 0)',
          700: 'oklch(37.1% 0 0)',
          800: 'oklch(26.9% 0 0)',
          900: 'oklch(20.5% 0 0)',
        },
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(38deg, rgba(209,144,73,1) 36%, rgba(191,65,41,1) 100%)',
        'gradient-blur': 'linear-gradient(55deg, rgba(209,144,73,1) 30%, rgba(191,65,41,1) 100%)',
      },
      borderRadius: {
        '999': '999px',
        '981': '981.72px',
      },
      blur: {
        '33': '33.05px',
        '97': '97.91px',
        '100': '100px',
        '159': '159.41px',
      },
    },
  },
  
  plugins: [
    require('@tailwindcss/container-queries'),
  ],
  
  // Disable base styles to prevent conflicts
  corePlugins: {
    preflight: false,
  },
}
