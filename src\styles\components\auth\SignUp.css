/* SignUp Page - Exact Figma Design */
.signup-page {
  min-height: 100vh;
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  position: relative;
  overflow-x: hidden;
}



/* Main Container */
.signup-container {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  padding: 10px 24px 40px;
}

.signup-content {
  width: 100%;
  max-width: 480px;
  background: transparent;
}

/* Header */
.signup-header {
  text-align: center;
  margin-bottom: 32px;
}

.signup-title {
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 36px;
  line-height: 44px;
  color: #FFFFFF;
  margin: 0 0 40px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Auth Tabs */
.auth-tabs {
  display: flex;
  gap: 0;
  background: #262626;
  border-radius: 12px;
  padding: 6px;
  margin-bottom: 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.auth-tab {
  flex: 1;
  padding: 14px 24px;
  background: transparent;
  border: none;
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
}

.auth-tab.active {
  background: #404040;
  border: 1px solid #525252;
}

/* Form Container */
.signup-form-container {
  background: transparent;
}

.form-title {
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 24px;
  line-height: 30px;
  text-align: center;
  color: #FFFFFF;
  margin: 0 0 40px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Social Login Buttons */
.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 24px;
  background: #262626;
  border: 1px solid #404040;
  border-radius: 12px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.social-btn:hover {
  background: #333333;
  border-color: #525252;
}

.social-icon {
  width: 20px;
  height: 20px;
  color: #FFFFFF;
}

/* Divider */
.divider {
  text-align: center;
  margin: 24px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #404040;
}

.divider span {
  background: #1A1A1A;
  padding: 0 16px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  position: relative;
  z-index: 1;
}

/* Form */
.signup-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #FFFFFF;
  margin: 0 0 8px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
}

.input-icon {
  width: 20px;
  height: 20px;
  color: #888888;
  flex-shrink: 0;
}

.form-input {
  width: 100%;
  height: 56px;
  padding: 0 56px 0 20px;
  background: #404040;
  border: 1px solid #525252;
  border-radius: 12px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #FFFFFF;
  box-sizing: border-box;
  transition: all 0.3s ease;
  text-indent: 0;
}

.form-input::placeholder {
  color: #888888;
  text-indent: 0;
  padding-left: 0;
}

.form-input:focus {
  outline: none;
  border-color: #BF4129;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  width: 16px;
  height: 16px;
  color: #888888;
  cursor: pointer;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
}

/* Password Requirements */
.password-requirements {
  margin-top: 8px;
}

.password-requirements p {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #FFFFFF;
  margin: 0 0 8px 0;
}

.password-requirements ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.password-requirements li {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #888888;
  margin-bottom: 4px;
  position: relative;
  padding-left: 16px;
}

.password-requirements li::before {
  content: '○';
  position: absolute;
  left: 0;
  color: #888888;
}

/* Forgot Password */
.forgot-password {
  margin-top: 8px;
  text-align: right;
}

.forgot-password-link {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #BF4129;
  text-decoration: none;
}

.forgot-password-link:hover {
  text-decoration: underline;
}

/* Submit Button */
.signup-btn {
  width: 100%;
  padding: 16px 24px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.signup-btn:hover {
  background: #A63622;
}

/* Demo User Section */
.demo-user-section {
  margin: 24px 0;
}

.demo-user-card {
  background: rgba(191, 65, 41, 0.1);
  border: 1px solid rgba(191, 65, 41, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.demo-user-info h3 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #FFFFFF;
  margin: 0 0 8px 0;
}

.demo-user-info p {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 16px 0;
}

.demo-credentials {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 16px;
}

.demo-credentials span {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(0, 0, 0, 0.2);
  padding: 4px 8px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
}

.demo-login-btn {
  width: 100%;
  padding: 12px 16px;
  background: rgba(191, 65, 41, 0.8);
  border: 1px solid #BF4129;
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
}

.demo-login-btn:hover {
  background: #BF4129;
  transform: translateY(-1px);
}

/* Error Message */
.error-message {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #ff6b6b;
  text-align: center;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .signup-page {
    padding: 0;
  }

  .signup-container {
    padding: 20px 16px;
    min-height: calc(100vh - 140px); /* Account for header and footer */
    justify-content: flex-start;
    padding-top: 40px;
  }

  .signup-content {
    max-width: 100%;
    width: 100%;
  }

  .signup-title {
    font-size: 20px;
    line-height: 24px;
    margin-bottom: 20px;
    text-align: left;
  }

  .form-title {
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 16px;
    text-align: left;
  }

  .auth-tabs {
    margin-bottom: 16px;
  }

  .auth-tab {
    padding: 8px 16px;
    font-size: 14px;
  }

  .social-login {
    gap: 8px;
    margin-bottom: 16px;
  }

  .social-btn {
    padding: 12px 16px;
    font-size: 14px;
    gap: 8px;
  }

  .social-icon {
    width: 16px;
    height: 16px;
  }

  .divider {
    margin: 16px 0;
  }

  .signup-form {
    gap: 16px;
  }

  .form-group {
    gap: 6px;
  }

  .form-input {
    padding: 12px 40px 12px 16px;
    font-size: 14px;
  }

  .input-icon {
    width: 16px;
    height: 16px;
  }

  .input-wrapper {
    gap: 8px;
  }

  .password-toggle {
    right: 12px;
    width: 14px;
    height: 14px;
  }

  .signup-btn {
    padding: 12px 16px;
    font-size: 14px;
    margin-top: 4px;
  }

  .password-requirements {
    margin-top: 6px;
  }

  .password-requirements p {
    font-size: 11px;
    margin-bottom: 6px;
  }

  .password-requirements li {
    font-size: 11px;
    margin-bottom: 2px;
  }

  .demo-user-section {
    margin: 16px 0;
  }

  .demo-user-card {
    padding: 16px;
    margin-bottom: 16px;
  }

  .demo-user-info h3 {
    font-size: 14px;
  }

  .demo-user-info p {
    font-size: 12px;
    margin-bottom: 12px;
  }

  .demo-credentials span {
    font-size: 11px;
  }

  .demo-login-btn {
    padding: 10px 12px;
    font-size: 12px;
  }

  .signup-container {
    padding: 5px 16px 30px;
    align-items: flex-start;
  }

  .signup-content {
    max-width: 100%;
    width: 100%;
  }

  .signup-title {
    font-size: 28px;
    line-height: 34px;
    margin-bottom: 32px;
  }

  .form-title {
    font-size: 20px;
    line-height: 26px;
    margin-bottom: 32px;
  }

  .auth-tabs {
    margin-bottom: 32px;
  }

  .social-btn {
    padding: 16px 20px;
    font-size: 15px;
  }

  .form-label {
    font-size: 15px;
    line-height: 19px;
  }

}

/* Forgot Password Link */
.forgot-password-link-container {
  text-align: right;
  margin: 16px 0 24px 0;
}

.forgot-password-link {
  background: none;
  border: none;
  color: #BF4129;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.3s ease;
}

.forgot-password-link:hover {
  color: #D19049;
  text-decoration: underline;
}
