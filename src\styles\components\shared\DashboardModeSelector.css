/* Dashboard Mode Selector - Tab Design */
.dashboard-mode-selector {
  font-family: 'Montserrat', sans-serif;
  margin-bottom: 16px;
  width: 100%;
}

.mode-selector-tabs {
  display: flex;
  gap: 8px;
  padding: 0;
  margin: 0;
  justify-content: flex-start;
}

.mode-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(64, 64, 64, 0.8);
  border: 1px solid #525252;
  border-radius: 12px;
  cursor: pointer !important;
  transition: all 0.3s ease;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
  min-width: 120px;
  justify-content: center;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  text-decoration: none;
}

.mode-tab:hover {
  background: rgba(82, 82, 82, 0.9);
  border-color: rgba(191, 65, 41, 0.5);
  transform: translateY(-1px);
  cursor: pointer !important;
}

.mode-tab.active {
  background: #BF4129;
  border-color: #BF4129;
  color: #FFFFFF;
  box-shadow: 0 2px 8px rgba(191, 65, 41, 0.3);
}

.mode-tab-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.mode-tab-icon img {
  width: 16px;
  height: 16px;
  object-fit: contain;
  filter: brightness(0) invert(1);
}

.mode-tab-label {
  font-size: 14px;
  font-weight: 600;
  color: inherit;
  white-space: nowrap;
}

/* Mobile Dropdown Styles */
.mode-selector-dropdown {
  position: relative;
  display: none;
  z-index: 1001;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 12px 16px;
  background: rgba(64, 64, 64, 0.8);
  border: 1px solid #525252;
  border-radius: 12px;
  cursor: pointer !important;
  transition: all 0.3s ease;
  font-family: 'Montserrat', sans-serif;
  color: #FFFFFF;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  pointer-events: auto;
  position: relative;
  z-index: 1001;
}

.dropdown-trigger:hover {
  background: rgba(82, 82, 82, 0.9);
  border-color: rgba(191, 65, 41, 0.5);
}

.dropdown-trigger-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dropdown-arrow {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.dropdown-arrow img {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(38, 38, 38, 0.95);
  border: 1px solid #525252;
  border-radius: 12px;
  margin-top: 4px;
  z-index: 1002;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 12px 16px;
  background: transparent;
  border: none;
  cursor: pointer !important;
  transition: all 0.3s ease;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
  border-radius: 0;
  text-decoration: none;
}

.dropdown-item:first-child {
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.dropdown-item:last-child {
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.dropdown-item:hover {
  background: rgba(82, 82, 82, 0.7);
  cursor: pointer !important;
}

.dropdown-item.active {
  background: #BF4129;
  color: #FFFFFF;
}

/* Desktop/Mobile Toggle */
.desktop-only {
  display: flex;
}

.mobile-only {
  display: none;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .dashboard-mode-selector {
    margin-bottom: 0;
    width: 100%;
  }

  .desktop-only {
    display: none;
  }

  .mobile-only {
    display: block;
  }

  .mode-selector-dropdown {
    display: block;
    margin-top: 0px !important; /* Remove top margin for better positioning */
    margin-bottom: 16px !important; /* Add bottom margin for spacing */
  }

  /* When used in mobile header, remove top margin */
  .mobile-dashboard-selector .dashboard-mode-selector {
    margin-top: 0;
  }

  .mobile-dashboard-selector .mode-selector-dropdown {
    margin-top: 0;
  }
}


