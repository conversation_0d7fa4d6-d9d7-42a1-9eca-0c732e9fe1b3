import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";

const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "ESVC - Grow Your Wealth. Get Funded. Stake and Earn",
  description: "Stake your ESVC tokens and earn daily ROI. Unlock exclusive opportunities to pitch your startup ideas for funding/get funded trade capital. Earn from our Bitcoin/Crypto Treasury",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${montserrat.variable} antialiased bg-neutral-900 text-neutral-100`}
      >
        {children}
      </body>
    </html>
  );
}
